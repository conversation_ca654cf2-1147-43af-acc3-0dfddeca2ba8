/* Custom styles for <PERSON><PERSON> Fresh */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Hero Slider Animations */
.slide {
    transform: translateX(0);
}

.slide.next {
    transform: translateX(100%);
}

.slide.prev {
    transform: translateX(-100%);
}

.slide-text {
    animation: slideInLeft 1s ease-out;
}

.slide-text-delay {
    animation: slideInLeft 1s ease-out 0.3s both;
}

.slide-buttons {
    animation: slideInLeft 1s ease-out 0.6s both;
}

.slide-image {
    animation: slideInRight 1s ease-out 0.4s both;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slider Dots */
.slider-dot.active {
    opacity: 1 !important;
    background-color: white;
}

/* Slider transitions */
.slide.active {
    opacity: 1;
    z-index: 2;
}

.slide:not(.active) {
    opacity: 0;
    z-index: 1;
}

/* Product card hover effects */
.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button animations */
.btn-primary {
    @apply bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 hover:bg-green-700 transform hover:scale-105;
}

.btn-secondary {
    @apply bg-transparent border-2 border-green-600 text-green-600 px-6 py-3 rounded-lg font-semibold transition duration-300 hover:bg-green-600 hover:text-white;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Form styles */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition duration-300;
}

.form-textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition duration-300 resize-vertical;
}

/* Parallax effect */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #22c55e;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #16a34a;
}
